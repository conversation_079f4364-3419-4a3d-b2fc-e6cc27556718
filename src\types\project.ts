// 任务状态枚举类型
export type TaskStatus = "未开始" | "进行中" | "已完成" | "已逾期" | "已驳回";

// 任务类型枚举
export type TaskType = "task" | "milestone" | "taskFolder";

// 任务优先级
export type TaskPriority = "低" | "普通" | "高" | "紧急";

// 项目状态
export type ProjectStatus =
  | "planning"
  | "active"
  | "completed"
  | "on_hold"
  | "cancelled";

// 上传后的文件信息
export interface UploadedFile {
  id: number; // 文件ID
  name: string; // 文件名称
  url: string; // 文件URL
  size?: number; // 文件大小(字节)
  uploadedAt: string; // 上传时间
  uploadedBy: number; // 上传人ID
}

// 任务数据类型（用于展示，包括子任务、文件等）
export interface TaskRow {
  id: number; // 任务ID
  title: string; // 任务标题
  type: TaskType; // 任务类型（taskFolder/task/milestone）
  priority: TaskPriority; // 任务优先级
  start: string; // 开始时间
  end: string; // 结束时间
  finish?: string | null; // 完成时间
  parentId?: number | null; // 父任务ID
  members?: number[]; // 成员ID列表
  children?: TaskRow[]; // 子任务（树结构）
  projectId: number; // 所属项目ID（必选）
  status: TaskStatus; // 任务状态（明确类型）
  owner: string; // 任务负责人（必选）
  canUpload: boolean; // 是否允许上传文件（必选）
  files?: UploadedFile[]; // 关联文件列表
}

// 任务表单类型（用于新增/编辑任务）
export interface TaskForm {
  id?: number; // 任务ID（编辑时用，新增可不传）
  title: string; // 任务标题
  type: TaskType; // 任务类型
  priority: TaskPriority; // 任务优先级
  start: string; // 开始时间
  end: string; // 结束时间
  finish?: string | null; // 完成时间
  parentId?: number | null; // 父任务ID
  members?: number[]; // 成员ID列表
  projectId: number; // 所属项目ID（必选）
  status: TaskStatus; // 任务状态
  owner: string; // 任务负责人（必选）
  canUpload: boolean; // 是否允许上传文件（必选）
  description?: string; // 任务描述（新增字段，可选）
}

// 项目信息类型
export interface Project {
  id: number; // 项目ID（必选）
  name: string; // 项目名称（必选）
  owner: string; // 项目负责人（必选）
  startDate: string | null; // 项目开始时间
  endDate: string | null; // 项目结束时间
  status: ProjectStatus; // 项目状态（明确类型，必选）
  tasks?: TaskRow[]; // 可选：关联的任务列表

  // 项目概览相关字段
  stats?: ProjectStat[]; // 项目统计数据
  milestones?: Milestone[]; // 项目里程碑
  statusChartData?: StatusChartItem[]; // 任务状态分布数据
  divisionChartData?: DivisionChartItem[]; // 任务分工数据
}

// 项目表单类型（用于新增/编辑项目）
export interface ProjectForm {
  name: string; // 项目名称（必选）
  owner: string; // 项目负责人（必选）
  startDate: string | null; // 项目开始时间
  endDate: string | null; // 项目结束时间
  status: ProjectStatus; // 项目状态（必选）
}

// 项目统计项类型
export interface ProjectStat {
  label: string; // 统计项标签
  value: number; // 统计值
  color: "blue" | "orange" | "red" | "green"; // 颜色标识
}

// 里程碑类型
export interface Milestone {
  id: number; // 里程碑ID（必选）
  label: string; // 里程碑名称（必选）
  date: string; // 日期（必选）
  completed: boolean; // 是否已完成（必选）
  sortOrder: number; // 排序顺序（必选）
  isCurrent?: boolean; // 是否为当前里程碑
  isToday?: boolean; // 是否为今天
  projectId: number; // 所属项目ID（必选）
}

// 状态图表数据项
export interface StatusChartItem {
  name: string; // 状态名称
  value: number; // 数量
}

// 分工图表数据项
export interface DivisionChartItem {
  name: string; // 人员名称
  value: number; // 任务数量
}

// 用户信息类型
export interface User {
  id: number; // 用户ID（必选）
  username: string; // 用户姓名（必选）
  avatar?: string; // 头像URL（可选）
  role?: string; // 角色（可选）
  email?: string; // 邮箱（可选）
}

// 文件上传参数
export interface UploadFileParams {
  taskId: number; // 任务ID（必选）
  files: File[]; // 上传的文件列表（必选）
}

// 项目概览数据接口
export interface ProjectOverviewData {
  stats: ProjectStat[];
  milestones: Milestone[];
  statusChartData: StatusChartItem[];
  divisionChartData: DivisionChartItem[];
}

// 任务统计数据类型
export interface TaskStats {
  totalTasks: number;
  pendingTasks: number;
  overdueTasks: number;
  completedTasks: number;
  projectTaskCounts: {
    projectId: number;
    projectName: string;
    taskCount: number;
  }[];
  yearlyTaskCounts: { year: number; taskCount: number }[];
}
export interface ApprovalFile {
  fileId: string;
  name: string;
  url: string;
}
